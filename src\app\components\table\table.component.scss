@use "../../../assets/styles/variables" as vars;

.table-header {
  th {
    background-color: vars.$neutral-light-grey !important;
    color: vars.$primary-blue-01 !important;
    border-bottom: none !important;
    &:first-child {
      border-top-left-radius: 12px !important;
    }
    &:last-child {
      border-top-right-radius: 12px !important;
    }
  }
}

.table-content-header td {
  background-color: #fff !important;
  color: #000 !important;
  border-top: none !important;
  border-bottom: 1px solid vars.$neutral-light-grey !important;

  &:first-child {
    border-left: 1.5px solid vars.$neutral-light-grey !important;
  }
  &:last-child {
    border-right: 1.5px solid vars.$neutral-light-grey !important;
  }
}

::ng-deep {
  .p-datatable-scrollable-body {
    border-left: 1px solid #000 !important;
    border-right: 1px solid #000 !important;
  }

  .p-datatable-scrollable-body table {
    border-collapse: separate !important;
  }

  .p-paginator {
    background-color: vars.$neutral-light-grey !important;
    border-radius: 0 0 12px 12px !important;
    .p-paginator-first.p-disabled,
    .p-paginator-prev.p-disabled,
    .p-paginator-next.p-disabled,
    .p-paginator-last.p-disabled {
      color: #a0a0a0 !important;
    }
    .p-paginator-page,
    .p-paginator-next,
    .p-paginator-last,
    .p-paginator-first,
    .p-paginator-prev {
      color: vars.$primary-blue-01 !important;
      &:not(.p-disabled):not(.p-paginator-page-selected):hover {
        background-color: vars.$primary-blue-01 !important;
        color: #fff !important;
      }
    }
    .p-paginator-page.p-paginator-page-selected {
      background-color: #a8a8f6 !important;
      color: #fff !important;
    }
    .p-select {
      background: vars.$neutral-light-grey !important;
      border: solid 1px vars.$primary-blue-01 !important;
      .p-select-label,
      .p-select-dropdown,
      .p-select-option {
        color: vars.$primary-blue-01 !important;
      }
      .p-select-list {
        background: vars.$neutral-light-grey !important;
      }
      .p-select-overlay {
        border: none !important;
      }
      .p-select-option {
        &.p-select-option-selected {
          background-color: #a8a8f6 !important;
          color: #fff !important;
          &.p-focus {
            background-color: #a8a8f6 !important;
            color: #fff !important;
          }
        }
        &:not(.p-select-option-selected):not(.p-disabled).p-focus {
          background: vars.$primary-blue-01 !important;
          color: #fff !important;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-left: 0 !important;

    .p-button {
      background: transparent !important;
      border: none !important;
      color: vars.$primary-blue-01 !important;
    }

    .action-btn {
      font-size: 12px !important;
      padding: 4px 8px !important;
      height: auto !important;
      min-width: auto !important;
      border: none !important;

      ::ng-deep .p-button-label {
        font-size: 12px !important;
        font-weight: 500 !important;
        text-decoration: underline !important;
      }

      &.confirm-btn {
        &:hover {
          transform: translateY(-1px);
        }
      }

      &.decline-btn {
        &:hover {
          transform: translateY(-1px);
        }
      }

      &.cancel-btn {
        &:hover {
          transform: translateY(-1px);
        }
      }
    }
  }
}
