<p-table [value]="products" [paginator]="true" [rows]="5" [tableStyle]="{ 'min-width': '50rem' }" [rowsPerPageOptions]="[5, 10, 20]">
    <ng-template #header>
        <tr class="table-header">
            <th>Practice ID</th>
            <th>Practice Name</th>
            <th>Status</th>
            <th>Actions</th>
        </tr>
    </ng-template>
    <ng-template #body let-product>
        <tr class="table-content-header">
            <td>{{ product.practiceId }}</td>
            <td>{{ product.practiceName }}</td>
            <td>{{ product.status }}</td>
            <td class="action-buttons">
                <p-button
                    label="Confirm"
                    variant="text"
                    size="small"
                    class="action-btn confirm-btn"
                    (onClick)="confirmIntegration(product)">
                </p-button>
                <p-button
                    label="Cancel"
                    variant="text"
                    size="small"
                    class="action-btn cancel-btn"
                    (onClick)="cancelIntegration(product)">
                </p-button>
                <p-button
                    label="Decline"
                    variant="text"
                    size="small"
                    class="action-btn decline-btn"
                    (onClick)="declineIntegration(product)">
                </p-button>
            </td>
        </tr>
    </ng-template>
</p-table>