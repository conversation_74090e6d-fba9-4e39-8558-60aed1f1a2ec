import { Component } from '@angular/core';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { IntegrationPractice } from 'src/app/Interfaces/integration-practice.interface';

@Component({
  selector: 'app-table',
  imports: [TableModule, ButtonModule],
  templateUrl: './table.component.html',
  styleUrl: './table.component.scss'
})
export class TableComponent {
  products: IntegrationPractice[] = [
    { practiceName: 'p1', practiceId: '1', status: },
    { practiceName: 'p2', practiceId: '2', status: 'Pending...'},
    { practiceName: 'p3', practiceId: '3', status: 'Pending...'},
    { practiceName: 'p4', practiceId: '4', status: 'Pending...'},
    { practiceName: 'p5', practiceId: '5', status: 'Pending...'},
    { practiceName: 'p6', practiceId: '6', status: 'Pending...'},
    { practiceName: 'p7', practiceId: '7', status: 'Pending...'},
    { practiceName: 'p8', practiceId: '8', status: 'Pending...'},
    { practiceName: 'p9', practiceId: '9', status: 'Pending...'},
    { practiceName: 'p10', practiceId: '10', status: 'Pending...' },
    { practiceName: 'p11', practiceId: '11', status: 'Pending...' },
    { practiceName: 'p12', practiceId: '12', status: 'Pending...' },
    { practiceName: 'p13', practiceId: '13', status: 'Pending...' },
    { practiceName: 'p14', practiceId: '14', status: 'Pending...' },
    { practiceName: 'p15', practiceId: '15', status: 'Pending...' },
    { practiceName: 'p16', practiceId: '16', status: 'Pending...' },
    { practiceName: 'p17', practiceId: '17', status: 'Pending...' },
    { practiceName: 'p18', practiceId: '18', status: 'Pending...' },
    { practiceName: 'p19', practiceId: '19', status: 'Pending...' },
    { practiceName: 'p20', practiceId: '20', status: 'Pending...' },
  ];

  confirmIntegration(product: any): void {
    console.log('Confirming integration for:', product);
    product.status = 'Active';
    // Implementare la logica per confermare l'integrazione
  }

  cancelIntegration(product: any): void {
    console.log('Canceling integration for:', product);
    product.status = 'Canceled';
    // Implementare la logica per cancellare l'integrazione
  }

  declineIntegration(product: any): void {
    console.log('Declining integration for:', product);
    product.status = 'Declined';
    // Implementare la logica per rifiutare l'integrazione
  }
}
